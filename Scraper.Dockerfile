FROM mcr.microsoft.com/playwright/python:v1.40.0

WORKDIR /app

# Copy requirements and install Python dependencies
COPY pumpfun-data/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install Playwright browsers
RUN playwright install chromium
RUN playwright install-deps chromium

# Copy the scraper service
COPY pumpfun-data/scraper_service.py .

# Set environment variables
ENV HEADLESS=true
ENV INTERVAL=5
ENV ENABLE_JSON_SCRAPING=true
ENV DATABASE_URL=**************************************/postgres

CMD ["python", "scraper_service.py"]