"""
Blackjack game logic and state management
"""
import random
import logging
from typing import List, Dict, Optional
from .models import Card, BlackjackGameState, BlackjackAction

logger = logging.getLogger(__name__)


class BlackjackGame:
    """Blackjack game logic and state management"""
    
    def __init__(self):
        self.state = BlackjackGameState()
        self.deck: List[Card] = []
        self._initialize_deck()
    
    def _initialize_deck(self):
        """Initialize a standard 52-card deck"""
        suits = ["hearts", "diamonds", "clubs", "spades"]
        ranks = ["A", "2", "3", "4", "5", "6", "7", "8", "9", "10", "J", "Q", "K"]
        
        self.deck = [Card(rank=rank, suit=suit) for suit in suits for rank in ranks]
        random.shuffle(self.deck)
    
    def _card_value(self, card: Card) -> int:
        """Get the numeric value of a card"""
        if card.rank in ["J", "Q", "K"]:
            return 10
        elif card.rank == "A":
            return 11  # Will be adjusted for aces later
        else:
            return int(card.rank)
    
    def _calculate_score(self, cards: List[Card]) -> int:
        """Calculate the score of a hand, handling aces properly"""
        score = 0
        aces = 0
        
        for card in cards:
            if card.rank == "A":
                aces += 1
                score += 11
            else:
                score += self._card_value(card)
        
        # Adjust for aces
        while score > 21 and aces > 0:
            score -= 10
            aces -= 1
        
        return score
    
    def _deal_card(self) -> Card:
        """Deal a card from the deck"""
        if not self.deck:
            self._initialize_deck()
        return self.deck.pop()
    
    def start_new_game(self) -> BlackjackGameState:
        """Start a new blackjack game"""
        self.state = BlackjackGameState()
        self._initialize_deck()
        
        # Deal initial cards
        self.state.player_cards = [self._deal_card(), self._deal_card()]
        self.state.dealer_cards = [self._deal_card()]  # Dealer gets one card initially
        
        # Calculate scores
        self.state.player_score = self._calculate_score(self.state.player_cards)
        self.state.dealer_score = self._calculate_score(self.state.dealer_cards)
        
        # Check if player can double or split
        self.state.can_double = len(self.state.player_cards) == 2
        self.state.can_split = (len(self.state.player_cards) == 2 and 
                               self.state.player_cards[0].rank == self.state.player_cards[1].rank)
        
        self.state.game_active = True
        self.state.votes = {}
        self.state.current_voters = []
        
        logger.info(f"New blackjack game started. Player score: {self.state.player_score}")
        return self.state
    
    def add_vote(self, username: str, action: BlackjackAction) -> bool:
        """Add a vote for an action"""
        if not self.state.game_active:
            return False
        
        # Remove previous vote from this user
        if username in self.state.current_voters:
            # Find and remove previous vote
            for vote_action in self.state.votes:
                if self.state.votes[vote_action] > 0:
                    self.state.votes[vote_action] -= 1
                    break
            self.state.current_voters.remove(username)
        
        # Add new vote
        if action.value not in self.state.votes:
            self.state.votes[action.value] = 0
        self.state.votes[action.value] += 1
        self.state.current_voters.append(username)
        
        logger.info(f"Vote added: {username} -> {action.value}")
        return True
    
    def execute_action(self, action: BlackjackAction) -> BlackjackGameState:
        """Execute a blackjack action"""
        if not self.state.game_active:
            return self.state
        
        if action == BlackjackAction.HIT:
            self._hit()
        elif action == BlackjackAction.STAND:
            self._stand()
        elif action == BlackjackAction.DOUBLE:
            self._double()
        elif action == BlackjackAction.SPLIT:
            self._split()
        
        # Clear votes after action
        self.state.votes = {}
        self.state.current_voters = []
        
        return self.state
    
    def _hit(self):
        """Player hits (takes another card)"""
        card = self._deal_card()
        self.state.player_cards.append(card)
        self.state.player_score = self._calculate_score(self.state.player_cards)
        
        # Can't double or split after hitting
        self.state.can_double = False
        self.state.can_split = False
        
        # Check for bust
        if self.state.player_score > 21:
            self.state.game_active = False
            self.state.game_result = "dealer_win"
        
        logger.info(f"Player hit. New score: {self.state.player_score}")
    
    def _stand(self):
        """Player stands (dealer plays)"""
        # Dealer draws until 17 or higher
        while self._calculate_score(self.state.dealer_cards) < 17:
            card = self._deal_card()
            self.state.dealer_cards.append(card)
        
        self.state.dealer_score = self._calculate_score(self.state.dealer_cards)
        
        # Determine winner
        if self.state.dealer_score > 21:
            self.state.game_result = "player_win"
        elif self.state.dealer_score > self.state.player_score:
            self.state.game_result = "dealer_win"
        elif self.state.player_score > self.state.dealer_score:
            self.state.game_result = "player_win"
        else:
            self.state.game_result = "push"
        
        self.state.game_active = False
        logger.info(f"Game ended. Result: {self.state.game_result}")
    
    def _double(self):
        """Player doubles down"""
        if self.state.can_double:
            card = self._deal_card()
            self.state.player_cards.append(card)
            self.state.player_score = self._calculate_score(self.state.player_cards)
            
            # After doubling, automatically stand
            if self.state.player_score <= 21:
                self._stand()
            else:
                self.state.game_active = False
                self.state.game_result = "dealer_win"
            
            logger.info(f"Player doubled. Final score: {self.state.player_score}")
    
    def _split(self):
        """Player splits (simplified - just treat as hit for now)"""
        # For simplicity, treat split as a hit
        # In a full implementation, this would create two hands
        self._hit()
        logger.info("Player split (treated as hit)")
    
    def get_state(self) -> BlackjackGameState:
        """Get current game state"""
        return self.state
