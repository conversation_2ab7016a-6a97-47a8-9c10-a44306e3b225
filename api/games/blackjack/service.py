"""
Blackjack game service for managing game state and integration
"""
import logging
from typing import Dict, Any
from .game import BlackjackGame
from .models import BlackjackAction, BlackjackVote
from ...core.state import app_state

logger = logging.getLogger(__name__)


class BlackjackService:
    """Service for managing blackjack game integration"""
    
    def __init__(self):
        self.game = BlackjackGame()
        # Register blackjack game state
        app_state.register_game("blackjack", self.game.get_state())
    
    def start_new_game(self) -> Dict[str, Any]:
        """Start a new blackjack game"""
        state = self.game.start_new_game()
        app_state.game_states["blackjack"] = state
        app_state.set_current_game("blackjack")
        
        logger.info("New blackjack game started")
        return {
            "status": "success",
            "message": "New blackjack game started",
            "game_state": state.dict()
        }
    
    def add_vote(self, vote: BlackjackVote) -> Dict[str, Any]:
        """Add a vote for a blackjack action"""
        success = self.game.add_vote(vote.username, vote.action)
        
        if success:
            # Update shared state
            app_state.game_states["blackjack"] = self.game.get_state()
            
            return {
                "status": "success",
                "message": f"Vote added: {vote.action.value}",
                "game_state": self.game.get_state().dict()
            }
        else:
            return {
                "status": "error",
                "message": "Failed to add vote - game not active"
            }
    
    def execute_action(self, action: BlackjackAction) -> Dict[str, Any]:
        """Execute a blackjack action"""
        state = self.game.execute_action(action)
        app_state.game_states["blackjack"] = state
        
        logger.info(f"Blackjack action executed: {action.value}")
        return {
            "status": "success",
            "message": f"Action executed: {action.value}",
            "game_state": state.dict()
        }
    
    def get_game_state(self) -> Dict[str, Any]:
        """Get current blackjack game state"""
        return {
            "game_state": self.game.get_state().dict(),
            "game_type": "blackjack"
        }
    
    def get_voting_summary(self) -> Dict[str, Any]:
        """Get current voting summary"""
        state = self.game.get_state()
        return {
            "votes": state.votes,
            "voter_count": len(state.current_voters),
            "game_active": state.game_active
        }


# Global blackjack service instance
blackjack_service = BlackjackService()
