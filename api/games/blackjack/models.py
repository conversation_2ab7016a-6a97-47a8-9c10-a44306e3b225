"""
Pydantic models specific to blackjack game
"""
from typing import List, Dict, Optional
from pydantic import BaseModel, Field
from enum import Enum


class BlackjackAction(str, Enum):
    """Valid blackjack actions"""
    HIT = "hit"
    STAND = "stand"
    DOUBLE = "double"
    SPLIT = "split"


class Card(BaseModel):
    """Model for a playing card"""
    rank: str = Field(..., description="Card rank (A, 2-10, J, Q, K)")
    suit: str = Field(..., description="Card suit (hearts, diamonds, clubs, spades)")


class BlackjackVote(BaseModel):
    """Model for blackjack voting"""
    username: str = Field(..., min_length=1, max_length=50)
    action: BlackjackAction
    mode: str = Field(..., description="Voting mode (anarchy, democracy)")


class BlackjackGameState(BaseModel):
    """Model for blackjack game state"""
    game_id: Optional[int] = None
    game_active: bool = False
    player_cards: List[Card] = []
    dealer_cards: List[Card] = []
    player_score: int = 0
    dealer_score: int = 0
    votes: Dict[str, int] = {}  # action -> vote count
    current_voters: List[str] = []
    game_result: Optional[str] = None
    can_double: bool = False
    can_split: bool = False
