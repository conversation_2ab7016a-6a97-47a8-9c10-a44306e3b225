"""
Global application state management
"""
from typing import Dict, List, Optional, Any
from fastapi import WebSocket
import psycopg2


class AppState:
    """Global application state shared between requests"""
    
    def __init__(self):
        # Database connection
        self.db_connection: Optional[psycopg2.connection] = None
        
        # WebSocket connections
        self.connected_websockets: List[WebSocket] = []
        
        # Chat system
        self.chat_messages: List[Dict] = []
        
        # Game states - each game type can register its state here
        self.game_states: Dict[str, Any] = {}
        
        # Current active game
        self.current_game: Optional[str] = None
    
    def register_game(self, game_name: str, initial_state: Any):
        """Register a new game type with its initial state"""
        self.game_states[game_name] = initial_state
    
    def get_game_state(self, game_name: str) -> Any:
        """Get the state for a specific game"""
        return self.game_states.get(game_name)
    
    def set_current_game(self, game_name: str):
        """Set the currently active game"""
        if game_name in self.game_states:
            self.current_game = game_name
        else:
            raise ValueError(f"Game '{game_name}' not registered")
    
    def get_current_game_state(self) -> Any:
        """Get the state of the currently active game"""
        if self.current_game:
            return self.game_states.get(self.current_game)
        return None


# Global state instance
app_state = AppState()
