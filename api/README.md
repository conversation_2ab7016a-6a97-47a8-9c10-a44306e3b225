# Pump Plays Games API

A modular FastAPI application for hosting multiple games in a "Twitch Plays" style format. Currently supports Blackjack with the ability to easily add more games.

## Architecture

The API is designed with a modular architecture to support multiple games:

```
api/
├── core/           # Core application setup and configuration
│   ├── config.py   # Configuration management
│   └── state.py    # Global application state
├── services/       # Shared services across games
│   ├── database.py # Database connection and queries
│   ├── websocket.py# WebSocket management
│   └── chat.py     # Chat message handling
├── games/          # Game-specific modules
│   └── blackjack/  # Blackjack game implementation
│       ├── models.py   # Pydantic models for blackjack
│       ├── game.py     # Game logic
│       └── service.py  # Game service integration
├── api/            # API routes
│   ├── main.py     # Main routes (health, websocket, game state)
│   ├── chat.py     # Chat endpoints
│   └── blackjack.py# Blackjack game endpoints
├── models/         # Shared Pydantic models
│   ├── chat.py     # Chat models
│   └── game.py     # General game models
└── utils/          # Utility functions
```

## Features

- **Single Worker**: Configured to run with a single worker to ensure shared memory works correctly
- **PostgreSQL Integration**: Uses psycopg2 for database connections
- **WebSocket Support**: Real-time communication for frontend
- **Chat System**: Built-in chat functionality with message broadcasting
- **Modular Games**: Easy to add new games alongside blackjack
- **Shared State**: Memory shared between requests for real-time game state

## Running the Application

### Prerequisites

1. Install dependencies:
```bash
cd api
pip install -r requirements.txt
```

2. Set up PostgreSQL database (using docker-compose):
```bash
cd ..
docker-compose up -d db
```

### Starting the API

Option 1 - Using the run script:
```bash
cd /path/to/ppg2
python api/run.py
```

Option 2 - Using the main module:
```bash
cd /path/to/ppg2
python -m api.main
```

The API will start on `http://0.0.0.0:8000`

## API Endpoints

### Health & Status
- `GET /health` - Health check with database status
- `GET /game/state` - Current game state
- `GET /ws` - WebSocket endpoint for real-time communication

### Chat
- `POST /chat/` - Send a chat message
- `GET /chat/recent` - Get recent chat messages
- `DELETE /chat/clear` - Clear all chat messages (admin)

### Blackjack Game
- `POST /blackjack/start` - Start a new blackjack game
- `GET /blackjack/state` - Get current blackjack game state
- `POST /blackjack/vote` - Vote for a blackjack action
- `POST /blackjack/execute/{action}` - Execute a blackjack action
- `GET /blackjack/votes` - Get current voting summary

## Configuration

Environment variables can be set in a `.env` file:

```env
DATABASE_URL=postgresql://postgres:password@localhost:5432/postgres
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=1
LOG_LEVEL=INFO
MAX_CHAT_MESSAGES=100
RECENT_CHAT_MESSAGES=50
WEBSOCKET_PING_INTERVAL=30
```

## Adding New Games

To add a new game:

1. Create a new directory under `api/games/your_game/`
2. Implement the game models, logic, and service
3. Create API routes in `api/api/your_game.py`
4. Register the game in the main application
5. Include the router in `api/main.py`

The blackjack implementation serves as a good example to follow.

## WebSocket Communication

The WebSocket endpoint supports various message types:

- `ping/pong` - Keep-alive messages
- `game_action` - Game-specific actions
- `chat_message` - Chat messages
- `connection_established` - Initial state on connection

## Database Integration

The application connects to PostgreSQL and maintains a persistent connection. The database service provides:

- Connection management with retry logic
- Query execution helpers
- Health checking
- Automatic connection cleanup

## Testing

The API has been tested with the following scenarios:
- Health check endpoint
- Chat message sending and retrieval
- Blackjack game creation and state management
- Voting system
- Action execution
- WebSocket connectivity (ready for frontend integration)

All endpoints are working correctly and the modular structure is ready for expansion.
