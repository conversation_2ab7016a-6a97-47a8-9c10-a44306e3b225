"""
Blackjack game API routes
"""
from fastapi import APIRouter, HTTPException
from ..games.blackjack.models import BlackjackVote, BlackjackAction
from ..games.blackjack.service import blackjack_service

router = APIRouter(prefix="/blackjack", tags=["blackjack"])


@router.post("/start")
async def start_new_game():
    """Start a new blackjack game"""
    try:
        result = blackjack_service.start_new_game()
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start new game: {str(e)}")


@router.get("/state")
async def get_game_state():
    """Get current blackjack game state"""
    try:
        return blackjack_service.get_game_state()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get game state: {str(e)}")


@router.post("/vote")
async def vote_for_action(vote: BlackjackVote):
    """Vote for a blackjack action"""
    try:
        result = blackjack_service.add_vote(vote)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to add vote: {str(e)}")


@router.post("/execute/{action}")
async def execute_action(action: BlackjackAction):
    """Execute a blackjack action (admin/automated)"""
    try:
        result = blackjack_service.execute_action(action)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to execute action: {str(e)}")


@router.get("/votes")
async def get_voting_summary():
    """Get current voting summary"""
    try:
        return blackjack_service.get_voting_summary()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get voting summary: {str(e)}")
