"""
WebSocket service for managing real-time connections
"""
import logging
from typing import Dict, Any, List
from fastapi import WebSocket, WebSocketDisconnect
import json

from ..core.state import app_state

logger = logging.getLogger(__name__)


class WebSocketService:
    """Service for managing WebSocket connections and broadcasting"""
    
    @staticmethod
    async def connect(websocket: WebSocket):
        """Accept a new WebSocket connection"""
        await websocket.accept()
        app_state.connected_websockets.append(websocket)
        logger.info(f"WebSocket connected. Total connections: {len(app_state.connected_websockets)}")
    
    @staticmethod
    def disconnect(websocket: WebSocket):
        """Remove a WebSocket connection"""
        if websocket in app_state.connected_websockets:
            app_state.connected_websockets.remove(websocket)
        logger.info(f"WebSocket disconnected. Total connections: {len(app_state.connected_websockets)}")
    
    @staticmethod
    async def send_to_websocket(websocket: WebSocket, message: Dict[str, Any]) -> bool:
        """Send a message to a specific WebSocket, return True if successful"""
        try:
            await websocket.send_json(message)
            return True
        except Exception as e:
            logger.warning(f"Failed to send message to WebSocket: {e}")
            return False
    
    @staticmethod
    async def broadcast(message: Dict[str, Any], exclude: WebSocket = None) -> int:
        """Broadcast a message to all connected WebSockets"""
        successful_sends = 0
        failed_websockets = []
        
        for websocket in app_state.connected_websockets:
            if exclude and websocket == exclude:
                continue
                
            success = await WebSocketService.send_to_websocket(websocket, message)
            if success:
                successful_sends += 1
            else:
                failed_websockets.append(websocket)
        
        # Remove failed WebSockets
        for failed_ws in failed_websockets:
            WebSocketService.disconnect(failed_ws)
        
        return successful_sends
    
    @staticmethod
    async def send_initial_state(websocket: WebSocket):
        """Send initial state to a newly connected WebSocket"""
        initial_message = {
            "type": "connection_established",
            "data": {
                "connected_clients": len(app_state.connected_websockets),
                "recent_messages": app_state.chat_messages[-10:],
                "current_game": app_state.current_game,
                "game_state": app_state.get_current_game_state()
            }
        }
        await WebSocketService.send_to_websocket(websocket, initial_message)
    
    @staticmethod
    async def handle_message(websocket: WebSocket, data: Dict[str, Any]):
        """Handle incoming WebSocket messages"""
        message_type = data.get("type")
        
        if message_type == "ping":
            await WebSocketService.send_to_websocket(websocket, {"type": "pong"})
        
        elif message_type == "game_action":
            # Broadcast game action to all other clients
            action_data = data.get("data", {})
            logger.info(f"Game action received: {action_data}")
            
            broadcast_message = {
                "type": "game_action",
                "data": action_data
            }
            await WebSocketService.broadcast(broadcast_message, exclude=websocket)
        
        elif message_type == "chat_message":
            # Handle chat messages through WebSocket
            from .chat import ChatService
            chat_data = data.get("data", {})
            await ChatService.process_websocket_message(chat_data)
        
        else:
            logger.warning(f"Unknown WebSocket message type: {message_type}")
    
    @staticmethod
    def get_connection_count() -> int:
        """Get the number of active WebSocket connections"""
        return len(app_state.connected_websockets)


# Convenience instance
ws_service = WebSocketService()
