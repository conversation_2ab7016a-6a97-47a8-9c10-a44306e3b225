import asyncio
import os
import json
import psycopg2
from psycopg2.extras import RealDictCursor
from playwright.async_api import async_playwright
from playwright_stealth import Stealth


class PumpFunScraper:
    def __init__(self, headless=True):
        self.browser = None
        self.page = None
        self.json_page = None
        self.headless = headless
        self.db_connection = None

    async def init(self, playwright):
        self.browser = await playwright.chromium.launch(
            headless=self.headless,
            args=['--no-sandbox', '--disable-setuid-sandbox', '--mute-audio']
        )
        self.page = await self.browser.new_page()
        self.json_page = await self.browser.new_page()
        print("✅ Browser initialized")
    
    async def init_db(self):
        """Initialize PostgreSQL connection"""
        try:
            db_url = os.getenv('DATABASE_URL', 'postgresql://postgres:password@localhost/postgres')
            self.db_connection = psycopg2.connect(db_url)
            print("✅ Database connection established")
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
    
    async def get_usernames_to_scrape(self, limit=10):
        """Poll external database for usernames that need wallet address data scraped"""
        if not self.db_connection:
            return []
        
        try:
            with self.db_connection.cursor(cursor_factory=RealDictCursor) as cursor:
                # Query for users that don't have wallet addresses
                cursor.execute("""
                    SELECT DISTINCT username 
                    FROM users 
                    WHERE wallet_address IS NULL 
                    LIMIT %s
                """, (limit,))
                
                result = cursor.fetchall()
                return [row['username'] for row in result]
                
        except Exception as e:
            print(f"❌ Error querying usernames: {e}")
            return []
    
    async def scrape_user_json(self, username):
        """Scrape JSON data for a specific username using the browser"""
        try:
            api_url = f"https://frontend-api-v3.pump.fun/users/{username}"
            print(f"🔍 Scraping JSON for user: {username}")
            
            # Navigate to JSON endpoint in browser (uses Cloudflare cookies from main page)
            await self.json_page.goto(api_url, wait_until='networkidle', timeout=30000)
            await asyncio.sleep(1)
            
            # Extract JSON response from page
            json_data = await self.json_page.evaluate("""
                () => {
                    try {
                        const pageText = document.body.innerText || document.body.textContent;
                        if (!pageText.trim()) return null;
                        return JSON.parse(pageText);
                    } catch (error) {
                        return { error: error.message, pageContent: document.body.innerText };
                    }
                }
            """)
            
            if json_data and 'error' not in json_data:
                print(f"✅ Successfully scraped JSON for {username}")
                return json_data
            else:
                print(f"⚠️ No valid JSON data found for {username}")
                return {'username': username, 'address': 'none'}
                return None
                
        except Exception as e:
            print(f"❌ Error scraping JSON for {username}: {e}")
            return None
    
    async def store_user_json_data(self, username, json_data):
        """Store scraped wallet address data in PostgreSQL"""
        if not self.db_connection:
            return False
        
        try:
            with self.db_connection.cursor() as cursor:
                # Update existing user's wallet address
                cursor.execute("""
                    UPDATE users 
                    SET wallet_address = %s, 
                        updated_at = NOW()
                    WHERE username = %s
                """, (
                    json_data.get('address'),
                    username
                ))
                
                # If no rows were updated, insert new user
                if cursor.rowcount == 0:
                    cursor.execute("""
                        INSERT INTO users (username, wallet_address, created_at, updated_at)
                        VALUES (%s, %s, NOW(), NOW())
                        ON CONFLICT (username) DO UPDATE SET
                            wallet_address = EXCLUDED.wallet_address,
                            updated_at = EXCLUDED.updated_at
                    """, (
                        username,
                        json_data.get('address')
                    ))
                
                self.db_connection.commit()
                print(f"💾 Stored wallet address for {username}")
                return True
                
        except Exception as e:
            print(f"❌ Error storing wallet data for {username}: {e}")
            self.db_connection.rollback()
            return False
    
    async def store_viewer_data(self, viewer_data):
        """Store viewer data in PostgreSQL"""
        if not self.db_connection or not viewer_data:
            return False
        
        try:
            with self.db_connection.cursor() as cursor:
                # Store each viewer, moderator, and host
                all_users = []
                all_users.extend(viewer_data.get('viewers', []))
                all_users.extend(viewer_data.get('moderators', []))
                all_users.extend(viewer_data.get('host', []))
                
                for username in all_users:
                    # Insert user if doesn't exist
                    cursor.execute("""
                        INSERT INTO users (username, created_at, updated_at)
                        VALUES (%s, NOW(), NOW())
                        ON CONFLICT (username) DO UPDATE SET
                            updated_at = NOW()
                    """, (username,))
                    
                    # Record user view activity
                    cursor.execute("""
                        INSERT INTO user_views (user_id, viewed_at)
                        SELECT id, NOW() FROM users WHERE username = %s
                    """, (username,))
                
                self.db_connection.commit()
                print(f"💾 Stored viewer data: {len(viewer_data.get('viewers', []))} viewers, {len(viewer_data.get('moderators', []))} mods, {len(viewer_data.get('host', []))} hosts")
                return True
                
        except Exception as e:
            print(f"❌ Error storing viewer data: {e}")
            self.db_connection.rollback()
            return False
    
    async def json_scraping_worker(self):
        """Background worker that continuously scrapes JSON data for users"""
        print("🚀 JSON scraping worker started")
        
        while True:
            try:
                usernames = await self.get_usernames_to_scrape()
                
                if usernames:
                    print(f"📋 Found {len(usernames)} users needing JSON scraping")
                    
                    for username in usernames:
                        json_data = await self.scrape_user_json(username)
                        if json_data:
                            await self.store_user_json_data(username, json_data)
                        
                        # Rate limiting
                        await asyncio.sleep(2)
                    
                    print(f"✅ Completed JSON scraping batch of {len(usernames)} users")
                else:
                    print("ℹ️ No users need JSON scraping right now")
                
                # Wait before next batch
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                print(f"❌ Error in JSON scraping worker: {e}")
                await asyncio.sleep(30)  # Wait 30s on error

    async def scrape_viewers(self, url):
        try:
            await self.page.goto(url, timeout=60000)
            await asyncio.sleep(3)
            
            # Click entry button if it exists
            try:
                print("🔍 Looking for 'I'm ready to pump' button...")
                button = await self.page.wait_for_selector('button[data-test-id="how-it-works-button"]', timeout=5000)
                if button:
                    await button.click()
                    print("✅ Clicked 'I'm ready to pump' button")
                    await asyncio.sleep(2)
            except:
                pass
            
            # Find video and hover
            video = await self.page.query_selector('video')
            if video:
                await video.hover()
                await asyncio.sleep(1)
            
            # Find and click live button
            live_selectors = ['#live-indicator', 'div[id="live-indicator"]', 'div.bg-livestream-green']
            live_button = None
            
            for selector in live_selectors:
                try:
                    live_button = await self.page.wait_for_selector(selector, timeout=2000)
                    if live_button:
                        break
                except:
                    continue
            
            if not live_button:
                raise Exception("Live button not found")
            
            await live_button.click()
            await asyncio.sleep(2)
            
            # Extract viewer data
            await self.page.wait_for_selector('div[role="dialog"]', timeout=10000)
            
            data = await self.page.evaluate("""
                () => {
                    const dialog = document.querySelector('div[role="dialog"]');
                    if (!dialog) return null;
                    
                    const participants = { host: [], moderators: [], viewers: [] };
                    const userElements = dialog.querySelectorAll('div[class*="flex h-[26px] items-center justify-between gap-2"]');
                    
                    userElements.forEach(element => {
                        const usernameEl = element.querySelector('p[class*="truncate whitespace-nowrap text-xs font-bold"]');
                        if (usernameEl) {
                            const username = usernameEl.textContent.trim();
                            const parentSection = element.closest('div[class*="flex max-w-xs flex-col gap-2"]');
                            
                            if (parentSection) {
                                const sectionHeader = parentSection.querySelector('div[class*="text-xs font-bold lowercase text-livestream-muted first-letter:uppercase"]');
                                if (sectionHeader) {
                                    const sectionText = sectionHeader.textContent.toLowerCase();
                                    if (sectionText.includes('host')) participants.host.push(username);
                                    else if (sectionText.includes('moderators')) participants.moderators.push(username);
                                    else if (sectionText.includes('viewers')) participants.viewers.push(username);
                                }
                            }
                        }
                    });
                    
                    return participants;
                }
            """)
            
            if data:
                total_count = len(data['viewers']) + len(data['moderators']) + len(data['host'])
                
                # If no viewers found, refresh page and try again
                if total_count == 0:
                    print("⚠️  No viewers found - refreshing page and retrying...")
                    await self.page.reload(wait_until='networkidle')
                    await asyncio.sleep(3)
                    
                    # Retry the entire scraping process once
                    try:
                        # Click entry button again if needed
                        try:
                            button = await self.page.wait_for_selector('button[data-test-id="how-it-works-button"]', timeout=5000)
                            if button:
                                await button.click()
                                await asyncio.sleep(2)
                        except:
                            pass
                        
                        # Find video and hover
                        video = await self.page.query_selector('video')
                        if video:
                            await video.hover()
                            await asyncio.sleep(1)
                        
                        # Find and click live button
                        for selector in live_selectors:
                            try:
                                live_button = await self.page.wait_for_selector(selector, timeout=2000)
                                if live_button:
                                    break
                            except:
                                continue
                        
                        if live_button:
                            await live_button.click()
                            await asyncio.sleep(2)
                            
                            # Re-extract viewer data
                            await self.page.wait_for_selector('div[role="dialog"]', timeout=10000)
                            data = await self.page.evaluate("""
                                () => {
                                    const dialog = document.querySelector('div[role="dialog"]');
                                    if (!dialog) return null;
                                    
                                    const participants = { host: [], moderators: [], viewers: [] };
                                    const userElements = dialog.querySelectorAll('div[class*="flex h-[26px] items-center justify-between gap-2"]');
                                    
                                    userElements.forEach(element => {
                                        const usernameEl = element.querySelector('p[class*="truncate whitespace-nowrap text-xs font-bold"]');
                                        if (usernameEl) {
                                            const username = usernameEl.textContent.trim();
                                            const parentSection = element.closest('div[class*="flex max-w-xs flex-col gap-2"]');
                                            
                                            if (parentSection) {
                                                const sectionHeader = parentSection.querySelector('div[class*="text-xs font-bold lowercase text-livestream-muted first-letter:uppercase"]');
                                                if (sectionHeader) {
                                                    const sectionText = sectionHeader.textContent.toLowerCase();
                                                    if (sectionText.includes('host')) participants.host.push(username);
                                                    else if (sectionText.includes('moderators')) participants.moderators.push(username);
                                                    else if (sectionText.includes('viewers')) participants.viewers.push(username);
                                                }
                                            }
                                        }
                                    });
                                    
                                    return participants;
                                }
                            """)
                            
                            total_count = len(data['viewers']) + len(data['moderators']) + len(data['host']) if data else 0
                    
                    except Exception as retry_error:
                        print(f"❌ Retry after refresh failed: {retry_error}")
                
                # Remove duplicates from viewers
                data['viewers'] = list(set(data['viewers']))

                print(f"📊 Found {len(data['viewers'])} viewers, {len(data['moderators'])} moderators, {len(data['host'])} hosts")
                
                # Store viewer data in database
                await self.store_viewer_data(data)
                
                return data
            
            await self.page.keyboard.press('Escape')
            return None
            
        except Exception as e:
            print(f"❌ Scraping error: {e}")
            return None

    async def monitor(self, url, interval_minutes=5, enable_json_scraping=True):
        print(f"🔄 Starting monitoring every {interval_minutes} minutes")
        print(f"🔍 JSON scraping: {'Enabled' if enable_json_scraping else 'Disabled'}")
        
        # Start JSON scraping worker in background if enabled
        json_worker_task = None
        if enable_json_scraping:
            json_worker_task = asyncio.create_task(self.json_scraping_worker())
        
        try:
            while True:
                try:
                    data = await self.scrape_viewers(url)
                    if data:
                        # Print first 5 viewers (or less if theres less viewers in the list)
                        print(f"👥 Current viewers: {data['viewers'][:5]}")
                    
                    await asyncio.sleep(interval_minutes * 60)
                    
                except KeyboardInterrupt:
                    break
                except Exception as e:
                    print(f"❌ Monitor error: {e}")
                    await asyncio.sleep(60)
        
        finally:
            # Clean up JSON worker
            if json_worker_task:
                json_worker_task.cancel()
                try:
                    await json_worker_task
                except asyncio.CancelledError:
                    pass

    async def close(self):
        if self.browser:
            await self.browser.close()
        if self.db_connection:
            self.db_connection.close()


async def main():
    url = os.getenv('PUMP_URL', 'https://pump.fun/coin/EpxanDRMd9iDEYdozB2CBV6tuS5mnYqBLuXrRbNcpump')
    headless = os.getenv('HEADLESS', 'true').lower() == 'true'
    interval = int(os.getenv('INTERVAL', '5'))
    enable_json_scraping = os.getenv('ENABLE_JSON_SCRAPING', 'true').lower() == 'true'
    
    scraper = PumpFunScraper(headless)
    
    async with Stealth().use_async(async_playwright()) as p:
        try:
            await scraper.init(p)
            await scraper.init_db()
            await scraper.monitor(url, interval, enable_json_scraping)
        finally:
            await scraper.close()


if __name__ == "__main__":
    asyncio.run(main())